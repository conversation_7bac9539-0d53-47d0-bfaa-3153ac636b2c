package com.batman.batdok.di

import com.batman.af3899.aeroevac.AeromedicalEvacuationMode
import com.batman.dd1380.TraumaMode
import com.batman.ems.EmsMode
import com.batman.k9.WorkingDogMode
import com.batman.sucmode.DNBIMode
import org.koin.dsl.module

val BrandModule = module {
    single {
        arrayOf(
            get<TraumaMode>(),
            get<DNBIMode>(),
            get<WorkingDogMode>(),
            get<AeromedicalEvacuationMode>(),
        )
    }
}