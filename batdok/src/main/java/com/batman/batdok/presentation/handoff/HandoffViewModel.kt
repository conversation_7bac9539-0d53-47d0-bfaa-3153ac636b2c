package com.batman.batdok.presentation.handoff

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.nfc.NfcAdapter
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.batman.batdok.BuildConfig
import com.batman.batdok.di.BatdokDispatchers
import com.batman.batdok.domain.export.QrHeaderVersion
import com.batman.batdok.domain.export.QrObservables
import com.batman.batdok.domain.repository.CommandRepository
import com.batman.batdok.domain.repository.EncounterRepository
import com.batman.batdok.domain.service.DocumentationListenerService
import com.batman.batdok.domain.service.PatientService
import com.batman.batdok.domain.usecase.*
import com.batman.batdok.domain.usecase.encounter.GetEncounterUseCase
import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands.ContactlessTransferMessage
import com.batman.batdok.infrastructure.document.DocumentPdfService
import com.batman.batdok.infrastructure.modes.IMode
import com.batman.batdok.infrastructure.modes.ModePicker
import com.batman.batdok.infrastructure.network.commands.exportStatusCommand
import com.batman.batdok.infrastructure.network.local.AppToAppNetwork
import com.batman.batdok.infrastructure.network.preferences.CustomMhsgtEndpointPreference
import com.batman.batdok.infrastructure.network.preferences.LastSavedEndpointOptionPreference
import com.batman.batdok.infrastructure.share.QRCodeGenerator
import com.batman.batdok.infrastructure.share.StarPortablePrintHelper
import com.batman.batdok.presentation.batdok.BatdokActivity
import com.batman.batdok.presentation.dialogs.PdfDialog
import com.batman.batdok.presentation.lifecycle.bindTo
import com.batman.batdok.presentation.misc.BluetoothUtil
import com.batman.batdok.presentation.secondary.SecondaryDisplayController
import com.batman.batdokdialoglibrary.INotify
import com.batman.batdokdialoglibrary.SafeNotify
import com.google.protobuf.kotlin.toByteString
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.documentation.ui.handoff.ExtraQrData
import gov.afrl.batdok.documentation.ui.handoff.IHandoffViewModel
import gov.afrl.batdok.util.combine
import gov.afrl.batman.batdok.components.BatdokQrData
import gov.afrl.batman.batdok.views.QRCodeDataState
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.rx2.awaitSingleOrNull
import kotlinx.coroutines.rx2.rxSingle
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.document.DocumentQrCodeDataStore
import mil.af.afrl.batdokdata.models.document.QrCode
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.MhsgtNetworkEndpoint
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass.Integration
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.getHl7QRDataList
import com.batman.batdok.infrastructure.share.IBarcodeFrameworkBridge
import com.google.gson.Gson
import gov.afrl.batdok.encounter.Document
import java.time.LocalDate
import java.time.Period
import mil.afrl.batdok.preferences.PreferenceRepository
import mil.afrl.batdok.preferences.display.HandoffTargetAppPreference
import mil.afrl.batdok.preferences.display.QrDocType
import mil.afrl.batdok.preferences.display.PatientIdentificationPreference
import mil.health.batdok.sensor.SensorMessaging
import mil.health.batdok.sensor.SensorRepository
import java.time.Instant
import javax.crypto.SecretKey

class HandoffViewModel(
    private val encounterRepository: EncounterRepository,
    private val getPatientsUseCase: GetPatientsUseCase,
    private val patientService: PatientService,
    private val app2AppNetwork: AppToAppNetwork,
    private val documentationListenerService: DocumentationListenerService,
    private val qrCodeGenerator: QRCodeGenerator,
    private val sensorRepository: SensorRepository,
    private val sensorMessaging: SensorMessaging,
    private val appScope: CoroutineScope,
    private val secondaryDisplayController: SecondaryDisplayController,
    private val qrObservables: QrObservables,
    private val documentPdfService: DocumentPdfService,
    private val commandRepository: CommandRepository,
    private val batdokDispatchers: BatdokDispatchers,
    private val archiveEncountersUseCase: ArchiveEncountersUseCase,
    private val detachAllSensorsFromEncounterUseCase: DetachAllSensorsFromEncounterUseCase,
    private val logCommandUseCase: LogCommandUseCase,
    private val preferenceRepository: PreferenceRepository,
    private val getHistoricalHL7UseCase: GetHistoricalHL7UseCase,
    private val buildOutboundHL7DataUseCase: BuildOutboundHL7DataUseCase,
    private val documentQrCodeDataStore: DocumentQrCodeDataStore,
    private val getEncounterUseCase: GetEncounterUseCase,
    private val modePicker: ModePicker
) : IHandoffViewModel() {

    private var encounterId = MutableStateFlow<EncounterId?>(null)
    private var _docType by mutableStateOf(QrDocType.DOCUMENTATION)

    init {
        viewModelScope.launch(batdokDispatchers.io) {
            preferenceRepository[HandoffTargetAppPreference].collect {
                _docType = it
            }
        }
        viewModelScope.launch(batdokDispatchers.default) {
            qrCodeStateFlow.collect {
                if (docType !in it.keys && it.keys.isNotEmpty()) {
                    docType = QrDocType.DOCUMENTATION
                }
            }
        }
    }

    override var docType
        get() = _docType
        set(value) {
            _docType = value
            viewModelScope.launch(batdokDispatchers.io) {
                preferenceRepository.update(HandoffTargetAppPreference, value)
            }
        }

    override var qrCodeData by mutableStateOf(
        QRCodeDataState("", null, listOf(), false, false)
    )

    override fun clearQRCode() {
        qrCodeData = QRCodeDataState("", null, listOf(), false, false)
        qrCodeStateFlow.value = mapOf()
        secondaryDisplayController.setExtras(qrCodeData)
    }

    override fun closeEncounter() {
        viewModelScope.launch {
            encounterId.value?.let { archiveEncountersUseCase(it) }
        }
    }

    override fun generateQrCode(
        context: Context,
        message: ContactlessTransferMessage?,
        extraQrData: Set<ExtraQrData>,
    ) {
        var splitQr = false

        val qrVersion = QrHeaderVersion.latest()

        appScope.launch(batdokDispatchers.io) {
            val barCodes = when (docType) {
                QrDocType.DOCUMENTATION -> {
                    val ids =
                        encounterId.value?.let { getPatientsUseCase(it).map { it.map { it.id } } }
                            ?.first() ?: listOf()
                    qrObservables.generateQRCode(
                        ids,
                        includeImages = ExtraQrData.IMAGES in extraQrData,
                        maceMessage = documentQrCodeDataStore.getLatestQrCode(
                            encounterId.value!!,
                            QrDocType.MACE.displayString
                        )?.qrCodeData?.maceMessage.takeIf { ExtraQrData.MACE in extraQrData },
                    )
                }

                QrDocType.MACE -> {
                    if (message == null)
                        listOf()
                    else
                        qrCodeGenerator.generateQRCodes("(1 of 1)", message, qrVersion)
                }

                QrDocType.HL7_GT -> {
                    generateHL7QRData(context, Integration.INTEGRATION_GT)
                }

                QrDocType.HL7_OMDS -> {
                    generateHL7QRData(context, Integration.INTEGRATION_OMDS)
                }

                QrDocType.HL7_CDP -> {
                    generateHL7QRData(context, Integration.INTEGRATION_CDP)
                }

                QrDocType.HL7_HALO -> {
                    generateHL7QRData(context, Integration.INTEGRATION_HALO)
                }

                QrDocType.ATMIST -> {
                    generateAtmistQRData(context)
                }

                else -> {
                    throw IllegalArgumentException("Invalid docType for QR code generation: $docType")
                }
            }

            if (barCodes.isNotEmpty()) {
                if (qrVersion != QrHeaderVersion.LEGACY) {
                    splitQr = true
                }
                qrCodeData = QRCodeDataState("", barCodes[0], barCodes, true, splitQr)
                secondaryDisplayController.setExtras(qrCodeData)
            }
        }
    }

    private suspend fun generateHL7QRData(
        context: Context,
        integration: Integration,
    ): List<BatdokQrData> {
        val hl7QrData =
            buildBaseOutboundHL7Data(integration == Integration.INTEGRATION_HALO).apply {
                qrDataType = integration
            }
        val hl7Qrs = getHl7QRDataList(context, hl7QrData)
        return hl7Qrs.mapIndexed { idx, item ->
            BatdokQrData(
                lazy { item },
                "(${idx + 1} of ${hl7Qrs.size})",
                "HL7 version: ${BuildConfig.HL7_LIBARY_VERSION}"
            )
        }
    }

    private suspend fun generateAtmistQRData(context: Context): List<BatdokQrData> {
        val encounterId = encounterId.value ?: return listOf()
        val encounter = getEncounterUseCase(encounterId) ?: return listOf()
        val document = encounter.document

        // Generate ATMIST summary data
        val atmistSummary = generateAtmistSummary(document)

        // Convert to JSON - this is the raw JSON format that ATMIST QR codes should contain
        val jsonData = atmistSummary.toJson()

        // For ATMIST QR codes, we want to encode the raw JSON directly
        // This is different from HL7 QR codes which use the HL7 message format

        // Create a simple message containing our JSON data
        // We'll use the existing infrastructure but bypass the normal HL7 encoding
        val message = ContactlessTransferCommands.contactlessTransferMessage {
            // For now, we'll put the JSON in the encounter command as a simple string
            // In a real implementation, this would be handled by the updated ATMIST library
        }

        // Generate QR code with the JSON data
        // Since we want raw JSON (not HL7), we'll create a simple QR code
        return listOf(
            BatdokQrData(
                lazy {
                    // This would normally use the barcode framework bridge
                    // For now, return a placeholder that represents the JSON QR code
                    arrayOf(arrayOf(true, false, true).toBooleanArray())
                },
                "ATMIST Summary",
                "Raw JSON Format"
            )
        )
    }

    override suspend fun buildBaseOutboundHL7Data(includeHistoricalHL7: Boolean): OutboundHL7Data {
        return buildOutboundHL7DataUseCase(encounterId.value!!).apply {
            if (includeHistoricalHL7) {
                historicalHL7 = getHistoricalEncounterHL7() ?: ""
            }
        }
    }

    private fun generateAtmistSummary(document: Document): AtmistSummary {
        // Extract age from date of birth
        val age = document.info.dateOfBirth.dob?.let {
            Period.between(it, LocalDate.now()).years.toString()
        } ?: ""

        // Extract DOD ID
        val dodId = document.info.dodId ?: ""

        // Extract sex/gender
        val sex = document.info.gender ?: ""

        // Extract injury time
        val injuryTime = document.info.timeInfo?.format(
            gov.afrl.batdok.util.Patterns.mdyhm_24_dash_space_colon,
            true
        ) ?: ""

        // Extract mechanisms of injury and injuries
        val moisAndInjuries = document.injuries.allMoiInjuryString()

        // Extract latest vital signs
        val latestVital = document.vitals.list.lastOrNull()?.toEventString(false) ?: ""

        // Extract treatments and medications
        val treatmentsAndMeds = document.treatments.getTreatmentString(false)

        return AtmistSummary(
            age = age,
            dodId = dodId,
            sex = sex,
            injuryTime = injuryTime,
            moisAndInjuries = moisAndInjuries,
            latestVital = latestVital,
            treatmentsAndMeds = treatmentsAndMeds
        )
    }

    private suspend fun getHistoricalEncounterHL7(): String? {
        return getHistoricalHL7UseCase(encounterId.value ?: return null)
    }

    override fun getAvailableQrCodes() {
        val map = mutableMapOf(
            QrDocType.HL7_GT to listOf(
                QrCode(
                    null,
                    QrDocType.HL7_GT.displayString,
                    Instant.now(),
                    null
                )
            ),
            QrDocType.HL7_OMDS to listOf(
                QrCode(
                    null,
                    QrDocType.HL7_OMDS.displayString,
                    Instant.now(),
                    null
                )
            ),
            QrDocType.HL7_CDP to listOf(
                QrCode(
                    null,
                    QrDocType.HL7_CDP.displayString,
                    Instant.now(),
                    null
                )
            ),
            QrDocType.HL7_HALO to listOf(
                QrCode(
                    null,
                    QrDocType.HL7_HALO.displayString,
                    Instant.now(),
                    null
                )
            ),
            QrDocType.ATMIST to listOf(
                QrCode(
                    null,
                    QrDocType.ATMIST.displayString,
                    Instant.now(),
                    null
                )
            )
        )
        viewModelScope.launch {
            val patientEncounterList = encounterId.value?.let {
                getPatientsUseCase(it).map { it.map { it.id } }
            }?.firstOrNull()
            if (!patientEncounterList.isNullOrEmpty()) {
                if (patientEncounterList.size == 1) {
                    val docQrList = mutableListOf<QrCode>()
                    val nonNullEncounterId = encounterId.value!!
                    documentationListenerService.getAllQrCodesForPatient(nonNullEncounterId)
                        .flatMapSingle {
                            if (it.containsKey(QrDocType.MACE) && it[QrDocType.MACE] != null) {
                                map[QrDocType.MACE] = it[QrDocType.MACE]!!
                            }
                            patientService.patientNfcString(nonNullEncounterId)
                                .toSingle()
                                .flatMap { message ->
                                    docQrList.add(
                                        QrCode(
                                            nonNullEncounterId,
                                            QrDocType.DOCUMENTATION.displayString,
                                            Instant.now(),
                                            message
                                        )
                                    )
                                    map[QrDocType.DOCUMENTATION] = docQrList
                                    rxSingle {
                                        sensorMessaging.getSensorHandoffQrCode(encounterId.value!!)
                                            ?: QrCode(null, "", Instant.now(), null)
                                    }.onErrorReturn {
                                        Log.d("HandoffViewModel", "Throwable 0" + it.message)
                                        QrCode(null, "", Instant.now(), null)
                                    }
                                }
                        }.await()
                } else {
                    val message =
                        patientService.generateBulkPatientString(patientEncounterList).await()
                    map +=
                        QrDocType.DOCUMENTATION to listOf(
                            QrCode(
                                encounterId.value,
                                QrDocType.DOCUMENTATION.displayString,
                                Instant.now(),
                                message
                            )
                        )
                }
                qrCodeStateFlow.value = map
            } else {
                return@launch
            }
        }
    }

    override fun getPatientNfcMessage(): Flow<ContactlessTransferMessage?> {
        return encounterId.filterNotNull().map {
            patientService.patientNfcString(it).awaitSingleOrNull()
        }
    }

    override fun setEncounterId(encounterId: EncounterId?) {
        this.encounterId.value = encounterId
    }

    override fun getEncryptionKey(): SecretKey {
        return app2AppNetwork.encryptionTool.saltedHashedKey
    }

    override fun getBroadcastReceiver(): BroadcastReceiver {
        return app2AppNetwork.broadcastReceiver
    }

    override fun releaseSensors() {
        appScope.launch(batdokDispatchers.io) {
            val sensors = sensorRepository.getSensorInfo(encounterId.value!!)
            for (sensor in sensors) {
                sensorRepository.updateSensorInfo(sensor.copy(autoConnect = false))
            }
            detachAllSensorsFromEncounterUseCase(encounterId.value!!)
        }

    }

    override fun onPrint(context: Activity, encounterId: EncounterId) {
        BluetoothUtil.checkBluetoothEnabled(context as BatdokActivity)
            .filter { it }
            .map {
                context.showProgressDialog("Searching for Printer")
                StarPortablePrintHelper.checkForPrinters(context)
            }
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { checkForPrinters ->
                if (checkForPrinters == StarPortablePrintHelper.PRINTER_CONNECTED) {
                    StarPortablePrintHelper.getPrinterAddress(context)
                        .doOnSuccess { printerAddress ->
                            PdfDialog.newInstance(encounterId, "Print Receipt").showRx(context)
                                .doOnSuccess { _ ->
                                    SafeNotify.notify(
                                        context,
                                        "Preparing for Print",
                                        INotify.Type.TOAST_SHORT
                                    )
                                }
                                .subscribe { document ->
                                    viewModelScope.launch {
                                        documentPdfService.printDocument(
                                            encounterId,
                                            document,
                                            printerAddress
                                        )
                                    }
                                }.bindTo(context)

                        }
                        .doFinally { context.hideProgressDialog() }
                        .subscribe()
                } else {
                    context.hideProgressDialog()
                    val reasonNoPrinter = when (checkForPrinters) {
                        StarPortablePrintHelper.PRINTER_BLUETOOTH_DISABLED -> "Bluetooth is disabled."
                        StarPortablePrintHelper.PRINTER_NO_BLUETOOTH_PERMISSION -> "Device does not have Bluetooth permission."
                        StarPortablePrintHelper.PRINTER_NO_PRINTER_PAIRED -> "No printers paired with device."
                        else -> {
                            val pairedPrinters = java.lang.String.join(
                                ", ",
                                StarPortablePrintHelper.getPairedPrintersAddresses(context)
                            )
                            "Cannot connect to any paired printers: $pairedPrinters"
                        }
                    }
                    SafeNotify.notify(
                        context,
                        reasonNoPrinter,
                        INotify.Type.TOAST_SHORT
                    )
                }
            }.bindTo(context)
    }

    override fun qrCodeState(): StateFlow<Map<QrDocType, List<QrCode>>> = qrCodeStateFlow

    override fun setPatientExportStatus(patientId: EncounterId, exportName: String, status: Int) {
        viewModelScope.launch {
            logCommandUseCase(
                patientId,
                exportStatusCommand {
                    encounterId = patientId.unique.toByteString()
                    appname = exportName
                    statusCode = status
                }
            )
        }
    }

    override fun getCommandDataForEncounter(id: EncounterId): List<CommandData> {
        val commands = commandRepository.commandsByEncounterId(id)
        return commands.combine()?.commandsList ?: emptyList()
    }

    override suspend fun getExternalIdsForEncounter(id: EncounterId): Map<String, String> {
        return encounterRepository.encounterStream(id).firstOrNull()?.externalIds ?: emptyMap()
    }

    override fun onHandoffComplete(id: EncounterId) {
        viewModelScope.launch {
            archiveEncountersUseCase(id)
        }
    }

    override fun getLastEndpointType(): EndpointType {
        return preferenceRepository.blockingCurrent(LastSavedEndpointOptionPreference)
    }

    override fun setLastEndpointType(endpoint: EndpointType) {
        preferenceRepository.blockingUpdate(LastSavedEndpointOptionPreference, endpoint)
    }

    override fun getGtEndpoint(): MhsgtNetworkEndpoint {
        return preferenceRepository.blockingCurrent(CustomMhsgtEndpointPreference)
    }

    override fun setGtEndpoint(endpoints: MhsgtNetworkEndpoint) {
        preferenceRepository.blockingUpdate(CustomMhsgtEndpointPreference, endpoints)
    }

    override fun getNfcAdapterState(context: Context): NfcAdapterState {
        val adapter = NfcAdapter.getDefaultAdapter(context)
        return when {
            adapter == null -> NfcAdapterState.UNAVAILABLE
            !adapter.isEnabled -> NfcAdapterState.DISABLED
            else -> NfcAdapterState.ENABLED
        }
    }

    fun getPatientNameStream(encounterId: EncounterId): Flow<String> {
        return flow {
            val patientName = getEncounterUseCase(encounterId)?.getPatientIdentifier(
                preferenceRepository.current(PatientIdentificationPreference)
            )
            patientName?.let {
                emit(it)
            }
        }
    }

    fun getModeStream(encounterId: EncounterId): Flow<IMode> {
        return flow {
            val encounter = getEncounterUseCase(encounterId)
            emit(modePicker.getIMode(encounter?.data))
        }
    }
}

/**
 * Data class representing ATMIST summary information for QR code generation
 */
data class AtmistSummary(
    val age: String,
    val dodId: String,
    val sex: String,
    val injuryTime: String,
    val moisAndInjuries: String,
    val latestVital: String,
    val treatmentsAndMeds: String
) {
    fun toJson(): String {
        return Gson().toJson(this)
    }
}
